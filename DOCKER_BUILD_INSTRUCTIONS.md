# Docker Build Instructions

## ⚠️ Important: Build Context

**You must run Docker builds from the ROOT directory of the monorepo, not from `apps/web`.**

The Dockerfile is designed to work with the entire monorepo structure and needs access to:
- Root `package.json` and `pnpm-lock.yaml`
- `turbo.json` configuration
- All packages in the `packages/` directory
- Workspace configuration files

## ✅ Correct Usage

### From Root Directory:
```bash
# Using the build script (recommended)
./scripts/docker-build.sh

# Using Docker directly
docker build -f apps/web/Dockerfile -t pluggou-web:latest .

# Using Docker Compose
docker-compose build web

# Using Make
make build
```

### ❌ Incorrect Usage (will fail):
```bash
# DON'T do this - wrong build context
cd apps/web
docker build -t pluggou-web:latest .
```

## 🚀 Quick Start

1. **Make sure you're in the root directory:**
   ```bash
   pwd  # Should show: /path/to/pluggou
   ls   # Should show: apps/ packages/ turbo.json package.json
   ```

2. **Build the image:**
   ```bash
   # Option 1: Use the optimized build script
   ./scripts/docker-build.sh

   # Option 2: Use Make
   make build

   # Option 3: Use Docker Compose
   docker-compose build web
   ```

3. **Run the container:**
   ```bash
   # Option 1: Use Docker Compose (recommended)
   docker-compose up web

   # Option 2: Use Make
   make run

   # Option 3: Use Docker directly
   docker run -p 3000:3000 pluggou-web:latest
   ```

## 🔧 Build Options

### Using the Build Script:
```bash
# Standard build
./scripts/docker-build.sh

# Custom image name and tag
./scripts/docker-build.sh --name my-app --tag v1.0.0

# Build without cache
./scripts/docker-build.sh --no-cache

# Build with more memory
./scripts/docker-build.sh --memory 8g

# See all options
./scripts/docker-build.sh --help
```

### Using Make:
```bash
make build          # Standard optimized build
make build-fast     # Fast build with cache
make build-no-cache # Clean build without cache
make run           # Run production container
make run-dev       # Run development container
make clean         # Clean up resources
make help          # See all commands
```

### Using Docker Compose:
```bash
# Production build and run
docker-compose up web

# Development build and run
docker-compose --profile dev up web-dev

# Build only
docker-compose build web

# Run in background
docker-compose up -d web
```

## 🐛 Troubleshooting

### Common Issues:

1. **"turbo.json not found" error:**
   - **Cause**: Building from wrong directory
   - **Solution**: Make sure you're in the root directory, not `apps/web`

2. **"package.json not found" error:**
   - **Cause**: Wrong build context
   - **Solution**: Use build context `.` (root directory)

3. **pnpm lockfile mismatch error:**
   - **Cause**: Lockfile inconsistencies between local and Docker environment
   - **Solution**: The Dockerfile now includes automatic fallback logic
   - **Manual fix**: Run `pnpm install` locally to update lockfile, then rebuild

4. **Build hangs or runs out of memory:**
   - **Solution**: Use `--memory 8g` flag or increase Docker memory limit

5. **Cache issues:**
   - **Solution**: Use `--no-cache` flag or run `make clean`

6. **"@biomejs/biome" dependency errors:**
   - **Cause**: Workspace dependency resolution issues in Docker
   - **Solution**: The Dockerfile now handles this automatically with fallback installation

### Debug Commands:
```bash
# Check current directory
pwd

# Verify files exist
ls -la turbo.json package.json pnpm-lock.yaml

# Check Docker build context
docker build --dry-run -f apps/web/Dockerfile .

# Monitor build progress
docker build --progress=plain -f apps/web/Dockerfile -t test .

# Check Docker resources
docker system df
```

## 📁 File Structure

The build expects this structure:
```
pluggou/                    # ← Build from here (root)
├── apps/
│   └── web/
│       ├── Dockerfile      # ← Dockerfile location
│       └── package.json
├── packages/
│   ├── api/
│   ├── auth/
│   └── ...
├── package.json            # ← Root package.json
├── pnpm-lock.yaml         # ← Root lockfile
├── turbo.json             # ← Turbo config
└── pnpm-workspace.yaml    # ← Workspace config
```

## 🎯 Performance Tips

1. **Use the build script**: It includes optimizations and error handling
2. **Enable BuildKit**: Automatically enabled in the script
3. **Use cache mounts**: Built into the Dockerfile
4. **Clean up regularly**: Use `make clean` to free up space
5. **Monitor resources**: Use `docker stats` to check usage

## 🔍 Verification

After building, verify the image works:
```bash
# Check image exists
docker images pluggou-web

# Test container startup
docker run -d --name test -p 3001:3000 pluggou-web:latest

# Check health
curl http://localhost:3001/api/health

# Clean up test
docker stop test && docker rm test
```

## 📚 Additional Resources

- [Docker Optimization Guide](./DOCKER_OPTIMIZATION_GUIDE.md)
- [Makefile Commands](./Makefile) - Run `make help`
- [Build Script Options](./scripts/docker-build.sh) - Run `./scripts/docker-build.sh --help`
