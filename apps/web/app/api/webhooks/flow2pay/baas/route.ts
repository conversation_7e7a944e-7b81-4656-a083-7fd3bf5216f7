import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { updateTransactionStatus } from "@repo/payments";
import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";
import { TransactionStatus } from "@prisma/client";
import { processAppWebhook } from "@repo/payments/src/webhooks/service";

/**
 * Flow2Pay webhook handler for PIX events
 * This route handles incoming webhooks from Flow2Pay payment gateway
 * Endpoint: /api/webhooks/flow2pay/baas
 */
export async function POST(request: NextRequest) {
  try {
    // Log the raw request for debugging
    const rawBody = await request.text();
    console.log("/api/webhooks/flow2pay/baas =================================>>> ");
    logger.info("Received Flow2Pay webhook at API endpoint", {
      rawBody,
      headers: Object.fromEntries(request.headers),
      url: request.url,
      method: request.method
    });

    // Parse the webhook payload
    let payload: any;
    try {
      payload = JSON.parse(rawBody);
      logger.info("Parsed Flow2Pay webhook payload", {
        evento: payload.evento,
        timestamp: payload.horario || payload.dataHora,
        txid: payload.txid,
        idEnvio: payload.idEnvio,
        status: payload.status,
        valor: payload.valor,
        endToEndId: payload.endToEndId
      });
    } catch (error) {
      logger.error("Failed to parse Flow2Pay webhook payload", { error, rawBody });
      return NextResponse.json({ error: "Invalid JSON payload" }, { status: 400 });
    }

    // Validate the webhook payload
    if (!payload.evento) {
      logger.error("Invalid Flow2Pay webhook payload format", { payload });
      return NextResponse.json({ error: "Invalid payload format - missing evento" }, { status: 400 });
    }

    // Validate event type
    const validEvents = ['PixIn', 'PixOut', 'PixInReversal', 'PixOutReversalExternal'];
    if (!validEvents.includes(payload.evento)) {
      logger.error("Invalid Flow2Pay webhook event type", { evento: payload.evento });
      return NextResponse.json({ error: "Invalid event type" }, { status: 400 });
    }

    // Validate webhook signature if eventToken is provided
    if (payload.token) {
      const expectedToken = process.env.FLOW2PAY_EVENT_TOKEN;
      if (expectedToken && payload.token !== expectedToken) {
        logger.error("Invalid Flow2Pay webhook token", { 
          providedToken: payload.token?.substring(0, 8) + "...",
          expectedExists: !!expectedToken
        });
        return NextResponse.json({ error: "Invalid authentication token" }, { status: 401 });
      }
    }

    // Normalize event type for internal processing
    let eventType: string;
    switch (payload.evento) {
      case 'PixIn':
        eventType = 'pixin';
        break;
      case 'PixOut':
        eventType = 'pixout';
        break;
      case 'PixInReversal':
        eventType = 'pixin_reversal';
        break;
      case 'PixOutReversalExternal':
        eventType = 'pixout_reversal';
        break;
      default:
        eventType = payload.evento.toLowerCase();
    }

    // Handle the event based on its normalized type
    logger.info(`Processing Flow2Pay webhook as ${eventType} event (original: ${payload.evento})`, {
      eventName: payload.evento,
      normalizedType: eventType,
      timestamp: payload.horario || payload.dataHora
    });

    if (eventType === 'pixin') {
      await handlePixInPayment(payload);
    } else if (eventType === 'pixout') {
      await handlePixOutTransfer(payload);
    } else if (eventType === 'pixin_reversal') {
      await handlePixInReversal(payload);
    } else if (eventType === 'pixout_reversal') {
      await handlePixOutReversal(payload);
    } else {
      logger.warn("Unhandled Flow2Pay webhook event type", {
        evento: payload.evento,
        eventType,
        data: payload
      });
    }

    // Return success response
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error processing Flow2Pay webhook", { error });
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Handle PixIn payment events (received payments)
 */
async function handlePixInPayment(payload: any) {
  const txid = payload.txid;
  const endToEndId = payload.endToEndId;
  const amount = payload.valor;
  const status = payload.status || 'Sucesso'; // Default to success for PixIn events
  
  logger.info("Processing Flow2Pay PixIn payment", {
    txid,
    endToEndId,
    amount,
    status,
    payerInfo: payload.pagador
  });

  // Map Flow2Pay status to internal transaction status
  let transactionStatus: TransactionStatus;
  switch (status) {
    case 'Sucesso':
      transactionStatus = TransactionStatus.COMPLETED;
      break;
    case 'Em processamento':
      transactionStatus = TransactionStatus.PENDING;
      break;
    case 'Falha':
    case 'Erro':
      transactionStatus = TransactionStatus.FAILED;
      break;
    default:
      transactionStatus = TransactionStatus.PENDING;
      break;
  }

  try {
    // Find the transaction by external ID (txid)
    const transaction = await db.transaction.findFirst({
      where: {
        OR: [
          { externalId: txid },
          { metadata: { path: ['txid'], equals: txid } },
          { metadata: { path: ['flow2pay_txid'], equals: txid } }
        ]
      },
      include: {
        organization: true
      }
    });

    if (transaction) {
      // Update existing transaction
      await updateTransactionStatus({
        transactionId: transaction.id,
        status: transactionStatus,
        metadata: {
          ...transaction.metadata as any,
          flow2pay_webhook: payload,
          endToEndId,
          flow2pay_status: status,
          updated_at: new Date().toISOString()
        }
      });

      // Store endToEndId if provided
      if (endToEndId && !transaction.endToEndId) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: { endToEndId }
        });
      }

      logger.info("Updated existing transaction from Flow2Pay PixIn webhook", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: transactionStatus,
        amount,
        endToEndId
      });

      // Trigger webhook notifications
      if (transactionStatus === TransactionStatus.COMPLETED) {
        await processAppWebhook({
          type: 'pix.payment.completed',
          payload: {
            id: transaction.id,
            organizationId: transaction.organizationId,
            amount: transaction.amount,
            status: transactionStatus,
            endToEndId,
            txid,
            type: 'PIX_IN',
            createdAt: transaction.createdAt,
            updatedAt: new Date()
          },
          organizationId: transaction.organizationId,
          transactionId: transaction.id
        });
      }
    } else {
      logger.warn("Transaction not found for Flow2Pay PixIn webhook", {
        txid,
        endToEndId,
        amount,
        status
      });
    }
  } catch (error) {
    logger.error("Error processing Flow2Pay PixIn webhook", {
      error,
      txid,
      endToEndId,
      amount,
      status
    });
    throw error;
  }
}

/**
 * Handle PixOut transfer events (sent payments)
 */
async function handlePixOutTransfer(payload: any) {
  const idEnvio = payload.idEnvio;
  const endToEndId = payload.endToEndId;
  const amount = payload.valor;
  const status = payload.status;
  
  logger.info("Processing Flow2Pay PixOut transfer", {
    idEnvio,
    endToEndId,
    amount,
    status,
    receiverInfo: payload.recebedor
  });

  // Map Flow2Pay status to internal transaction status
  let transactionStatus: TransactionStatus;
  switch (status) {
    case 'Sucesso':
      transactionStatus = TransactionStatus.COMPLETED;
      break;
    case 'Em processamento':
      transactionStatus = TransactionStatus.PENDING;
      break;
    case 'Falha':
    case 'Erro':
      transactionStatus = TransactionStatus.FAILED;
      break;
    default:
      transactionStatus = TransactionStatus.PENDING;
      break;
  }

  try {
    // Find the transaction by external ID (idEnvio)
    const transaction = await db.transaction.findFirst({
      where: {
        OR: [
          { externalId: idEnvio },
          { metadata: { path: ['idEnvio'], equals: idEnvio } },
          { metadata: { path: ['flow2pay_id_envio'], equals: idEnvio } }
        ]
      },
      include: {
        organization: true
      }
    });

    if (transaction) {
      // Update existing transaction
      await updateTransactionStatus({
        transactionId: transaction.id,
        status: transactionStatus,
        metadata: {
          ...transaction.metadata as any,
          flow2pay_webhook: payload,
          endToEndId,
          flow2pay_status: status,
          updated_at: new Date().toISOString()
        }
      });

      // Store endToEndId if provided
      if (endToEndId && !transaction.endToEndId) {
        await db.transaction.update({
          where: { id: transaction.id },
          data: { endToEndId }
        });
      }

      logger.info("Updated existing transaction from Flow2Pay PixOut webhook", {
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        status: transactionStatus,
        amount,
        endToEndId
      });

      // Trigger webhook notifications
      await processAppWebhook({
        type: transactionStatus === TransactionStatus.COMPLETED ? 'pix.transfer.completed' : 
              transactionStatus === TransactionStatus.FAILED ? 'pix.transfer.failed' : 'pix.transfer.updated',
        payload: {
          id: transaction.id,
          organizationId: transaction.organizationId,
          amount: transaction.amount,
          status: transactionStatus,
          endToEndId,
          idEnvio,
          type: 'PIX_OUT',
          createdAt: transaction.createdAt,
          updatedAt: new Date()
        },
        organizationId: transaction.organizationId,
        transactionId: transaction.id
      });
    } else {
      logger.warn("Transaction not found for Flow2Pay PixOut webhook", {
        idEnvio,
        endToEndId,
        amount,
        status
      });
    }
  } catch (error) {
    logger.error("Error processing Flow2Pay PixOut webhook", {
      error,
      idEnvio,
      endToEndId,
      amount,
      status
    });
    throw error;
  }
}

/**
 * Handle PixIn reversal events (refunds of received payments)
 */
async function handlePixInReversal(payload: any) {
  const endToEndId = payload.endToEndId;
  const amount = payload.valor;
  const status = payload.status;
  
  logger.info("Processing Flow2Pay PixIn reversal", {
    endToEndId,
    amount,
    status
  });

  // Map Flow2Pay status to internal transaction status
  const transactionStatus = status === 'Sucesso' ? TransactionStatus.COMPLETED : TransactionStatus.FAILED;

  try {
    // Find the original transaction by endToEndId
    const originalTransaction = await db.transaction.findFirst({
      where: {
        endToEndId,
        type: 'CHARGE'
      },
      include: {
        organization: true
      }
    });

    if (originalTransaction) {
      // Create a reversal transaction
      const reversalTransaction = await db.transaction.create({
        data: {
          organizationId: originalTransaction.organizationId,
          type: 'REFUND',
          amount: Math.abs(amount),
          status: transactionStatus,
          description: 'Estorno de PIX recebido',
          externalId: payload.idEnvio || payload.txid,
          endToEndId,
          gatewayId: originalTransaction.gatewayId,
          metadata: {
            flow2pay_webhook: payload,
            original_transaction_id: originalTransaction.id,
            flow2pay_status: status,
            reversal_type: 'PixInReversal'
          }
        }
      });

      logger.info("Created reversal transaction from Flow2Pay PixIn reversal webhook", {
        reversalTransactionId: reversalTransaction.id,
        originalTransactionId: originalTransaction.id,
        organizationId: originalTransaction.organizationId,
        status: transactionStatus,
        amount,
        endToEndId
      });

      // Trigger webhook notifications
      await processAppWebhook({
        type: 'pix.reversal.completed',
        payload: {
          id: reversalTransaction.id,
          originalTransactionId: originalTransaction.id,
          organizationId: originalTransaction.organizationId,
          amount: Math.abs(amount),
          status: transactionStatus,
          endToEndId,
          type: 'PIX_IN_REVERSAL',
          createdAt: reversalTransaction.createdAt,
          updatedAt: reversalTransaction.updatedAt
        },
        organizationId: originalTransaction.organizationId,
        transactionId: reversalTransaction.id
      });
    } else {
      logger.warn("Original transaction not found for Flow2Pay PixIn reversal webhook", {
        endToEndId,
        amount,
        status
      });
    }
  } catch (error) {
    logger.error("Error processing Flow2Pay PixIn reversal webhook", {
      error,
      endToEndId,
      amount,
      status
    });
    throw error;
  }
}

/**
 * Handle PixOut reversal events (refunds of sent payments)
 */
async function handlePixOutReversal(payload: any) {
  const endToEndId = payload.endToEndId;
  const amount = payload.valor;
  const status = payload.status;
  
  logger.info("Processing Flow2Pay PixOut reversal", {
    endToEndId,
    amount,
    status
  });

  // Map Flow2Pay status to internal transaction status
  const transactionStatus = status === 'Sucesso' ? TransactionStatus.COMPLETED : TransactionStatus.FAILED;

  try {
    // Find the original transaction by endToEndId
    const originalTransaction = await db.transaction.findFirst({
      where: {
        endToEndId,
        type: 'SEND'
      },
      include: {
        organization: true
      }
    });

    if (originalTransaction) {
      // Create a reversal transaction
      const reversalTransaction = await db.transaction.create({
        data: {
          organizationId: originalTransaction.organizationId,
          type: 'REFUND',
          amount: Math.abs(amount),
          status: transactionStatus,
          description: 'Estorno de PIX enviado',
          externalId: payload.idEnvio || payload.txid,
          endToEndId,
          gatewayId: originalTransaction.gatewayId,
          metadata: {
            flow2pay_webhook: payload,
            original_transaction_id: originalTransaction.id,
            flow2pay_status: status,
            reversal_type: 'PixOutReversalExternal'
          }
        }
      });

      logger.info("Created reversal transaction from Flow2Pay PixOut reversal webhook", {
        reversalTransactionId: reversalTransaction.id,
        originalTransactionId: originalTransaction.id,
        organizationId: originalTransaction.organizationId,
        status: transactionStatus,
        amount,
        endToEndId
      });

      // Trigger webhook notifications
      await processAppWebhook({
        type: 'pix.reversal.completed',
        payload: {
          id: reversalTransaction.id,
          originalTransactionId: originalTransaction.id,
          organizationId: originalTransaction.organizationId,
          amount: Math.abs(amount),
          status: transactionStatus,
          endToEndId,
          type: 'PIX_OUT_REVERSAL',
          createdAt: reversalTransaction.createdAt,
          updatedAt: reversalTransaction.updatedAt
        },
        organizationId: originalTransaction.organizationId,
        transactionId: reversalTransaction.id
      });
    } else {
      logger.warn("Original transaction not found for Flow2Pay PixOut reversal webhook", {
        endToEndId,
        amount,
        status
      });
    }
  } catch (error) {
    logger.error("Error processing Flow2Pay PixOut reversal webhook", {
      error,
      endToEndId,
      amount,
      status
    });
    throw error;
  }
}
