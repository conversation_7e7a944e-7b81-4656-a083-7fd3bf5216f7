import { PageHeader } from "@saas/shared/components/PageHeader";
import { getActiveOrganization } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { notFound } from "next/navigation";
import { Button } from "@ui/components/button";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";
import { AcquirerCredentialsForm } from "./AcquirerCredentialsForm";

interface CredentialField {
  key: string;
  label: string;
  type: string;
  options?: { value: string; label: string }[];
}

interface AcquirerInfoType {
  name: string;
  description: string;
  credentials: CredentialField[];
  webhookUrl: string;
  docs: string;
}

export default async function AcquirerConfigPage({
  params,
}: {
  params: { organizationSlug: string; gateway: string };
}) {
  const { organizationSlug, gateway: acquirerId } = params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  // Define acquirer configurations - match with implemented providers
  const acquirerConfigs: Record<string, AcquirerInfoType> = {
    flow2pay: {
      name: "Flow2Pay",
      description: "Integração direta com Flow2Pay para PIX com performance otimizada.",
      credentials: [
        { key: "clientId", label: "Client ID", type: "text" },
        { key: "clientSecret", label: "Client Secret", type: "password" },
        { key: "eventToken", label: "Event Token", type: "password" },
        { key: "apiUrl", label: "API URL", type: "text", defaultValue: "https://pixv2.flow2pay.com.br" }
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/flow2pay/baas`,
      docs: "https://docs-pix.flow2pay.com.br/",
    },
    mercadopago: {
      name: "Mercado Pago",
      description: "Plataforma de pagamentos do Mercado Livre com suporte completo a Pix.",
      credentials: [
        { key: "accessToken", label: "Access Token", type: "password" },
        { key: "environment", label: "Ambiente", type: "select", options: [
          { value: "sandbox", label: "Sandbox" },
          { value: "production", label: "Produção" }
        ]}
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/mercadopago`,
      docs: "https://www.mercadopago.com.br/developers/pt/reference",
    },
    asaas: {
      name: "Asaas",
      description: "Plataforma completa de pagamentos com foco em Pix e cobranças recorrentes.",
      credentials: [
        { key: "apiKey", label: "API Key", type: "password" },
        { key: "environment", label: "Ambiente", type: "select", options: [
          { value: "sandbox", label: "Sandbox" },
          { value: "production", label: "Produção" }
        ]}
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/asaas`,
      docs: "https://docs.asaas.com/reference/",
    },
    reflowpay: {
      name: "ReflowPay",
      description: "Processador de pagamentos com foco em Pix e métodos alternativos.",
      credentials: [
        { key: "publicKey", label: "Chave Pública", type: "text" },
        { key: "secretKey", label: "Chave Secreta", type: "password" },
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/reflowpay`,
      docs: "https://staging.api.reflowpay.com/docs",
    },
    primepag: {
      name: "PrimePag",
      description: "Adquirente de pagamentos completo para e-commerce e marketplaces.",
      credentials: [
        { key: "clientId", label: "Client ID", type: "text" },
        { key: "clientSecret", label: "Client Secret", type: "password" },
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/primepag`,
      docs: "https://docs.primepag.com.br",
    },
    pixium: {
      name: "Pixium",
      description: "Plataforma especializada em pagamentos Pix para empresas de todos os portes.",
      credentials: [
        { key: "apiKey", label: "API Key", type: "password" },
        { key: "companyId", label: "Company ID", type: "text" },
        { key: "environment", label: "Ambiente", type: "select", options: [
          { value: "sandbox", label: "Sandbox" },
          { value: "production", label: "Produção" }
        ]}
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/pixium`,
      docs: "https://pixium.readme.io/reference/",
    },
    transfeera: {
      name: "Transfeera",
      description: "Solução completa para transferências e pagamentos via Pix.",
      credentials: [
        { key: "clientId", label: "Client ID", type: "text" },
        { key: "clientSecret", label: "Client Secret", type: "password" },
        { key: "environment", label: "Ambiente", type: "select", options: [
          { value: "sandbox", label: "Sandbox" },
          { value: "production", label: "Produção" }
        ]}
      ],
      webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/transfeera`,
      docs: "https://docs.transfeera.dev/docs/comece-por-aqui-introducao",
    },
  };

  // Get acquirer info or use default
  const acquirerInfo = acquirerConfigs[acquirerId] || {
    name: acquirerId.charAt(0).toUpperCase() + acquirerId.slice(1),
    description: "Configure suas credenciais para este adquirente de pagamento.",
    credentials: [
      { key: "apiKey", label: "API Key", type: "text" },
    ],
    webhookUrl: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/webhooks/${acquirerId}`,
    docs: "",
  };

  // Try to get existing acquirer configuration
  let acquirerData = null;
  let credentials: Record<string, string> = {};
  let isActive = false;
  let isDefault = false;
  let priority = 999;

  try {
    // Using try/catch to handle database errors
    const result = await db.$queryRaw<{
      id: string;
      name: string;
      type: string;
      credentials: any;
      isActive: boolean;
      isDefault: boolean;
      priority?: number;
    }[]>`
      SELECT * FROM "payment_gateway"
      WHERE "organizationId" = ${organization.id}
      AND "type" = ${acquirerId.toUpperCase()}
      LIMIT 1
    `;

    if (result && result.length > 0) {
      acquirerData = result[0];
      credentials = acquirerData.credentials as Record<string, string>;
      isActive = acquirerData.isActive;
      isDefault = acquirerData.isDefault;
      priority = acquirerData.priority || 999;
    }
  } catch (error) {
    console.error(`Error fetching acquirer configuration for ${acquirerId}:`, error);
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" size="sm" className="mr-2" asChild>
          <Link href={`/app/${organizationSlug}/integrations/gateways`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Voltar
          </Link>
        </Button>
      </div>

      <PageHeader
        title={`Configuração - ${acquirerInfo.name}`}
        subtitle="Configure suas credenciais e opções para este adquirente de pagamento"
      />

      <div className="space-y-6">
        <AcquirerCredentialsForm
          acquirerId={acquirerId}
          organizationId={organization.id}
          acquirerInfo={acquirerInfo}
          initialCredentials={credentials}
          initialActive={isActive}
          initialDefault={isDefault}
          initialPriority={priority}
        />
      </div>
    </div>
  );
}
