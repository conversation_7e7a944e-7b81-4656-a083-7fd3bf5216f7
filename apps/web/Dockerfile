# Use specific Node.js version with Alpine for smaller image size
FROM node:22-alpine AS base

# Install system dependencies and enable corepack in a single layer
RUN apk add --no-cache libc6-compat \
    && apk update \
    && corepack enable

# Set pnpm environment
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install turbo globally in base image for better caching
RUN pnpm add -g turbo

# Set working directory
WORKDIR /app

# ============================================
# PRUNER STAGE - Extract only needed files
# ============================================
FROM base AS pruner

# Copy only the files needed for pruning (better cache invalidation)
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./
COPY apps/web/package.json ./apps/web/package.json
COPY packages/*/package.json ./packages/*/
COPY config/package.json ./config/package.json
COPY tooling/*/package.json ./tooling/*/

# Prune the workspace for the web app
RUN turbo prune @repo/web --docker

# ============================================
# INSTALLER STAGE - Install dependencies
# ============================================
FROM base AS installer

# Copy pruned workspace files
COPY --from=pruner /app/out/json/ .
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml

# Set environment variables to optimize installation
ENV CYPRESS_INSTALL_BINARY=0
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV NEXT_TELEMETRY_DISABLED=1

# Install dependencies with optimizations
# Try frozen lockfile first, fallback to regular install if it fails
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile --prefer-offline || \
    (echo "Frozen lockfile failed, trying regular install..." && \
    pnpm install --prefer-offline)

# ============================================
# BUILDER STAGE - Build the application
# ============================================
FROM installer AS builder

# Copy source code
COPY --from=pruner /app/out/full/ .

# Build the application with optimizations
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN --mount=type=cache,id=turbo,target=.turbo \
    pnpm turbo run build --filter=@repo/web...

# ============================================
# RUNNER STAGE - Production runtime
# ============================================
FROM node:22-alpine AS runner

# Install only runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    && addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

WORKDIR /app

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Copy built application with correct ownership
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "apps/web/server.js"]
