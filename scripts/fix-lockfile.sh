#!/bin/bash

# Fix pnpm lockfile issues for Docker builds
# This script helps resolve lockfile mismatches that can occur in Docker environments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v pnpm &> /dev/null; then
        print_error "pnpm is not installed or not in PATH"
        exit 1
    fi
    
    if [[ ! -f "package.json" ]]; then
        print_error "package.json not found. Make sure you're in the root directory."
        exit 1
    fi
    
    if [[ ! -f "pnpm-workspace.yaml" ]]; then
        print_error "pnpm-workspace.yaml not found. This doesn't appear to be a pnpm workspace."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to backup current lockfile
backup_lockfile() {
    if [[ -f "pnpm-lock.yaml" ]]; then
        print_status "Backing up current lockfile..."
        cp pnpm-lock.yaml "pnpm-lock.yaml.backup.$(date +%Y%m%d_%H%M%S)"
        print_success "Lockfile backed up"
    fi
}

# Function to clean and regenerate lockfile
regenerate_lockfile() {
    print_status "Cleaning node_modules and lockfile..."
    
    # Remove node_modules from all packages
    find . -name "node_modules" -type d -prune -exec rm -rf {} \; 2>/dev/null || true
    
    # Remove lockfile
    rm -f pnpm-lock.yaml
    
    print_status "Regenerating lockfile..."
    pnpm install
    
    print_success "Lockfile regenerated successfully"
}

# Function to verify lockfile
verify_lockfile() {
    print_status "Verifying lockfile consistency..."
    
    if pnpm install --frozen-lockfile --dry-run; then
        print_success "Lockfile is consistent"
        return 0
    else
        print_warning "Lockfile still has issues"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Fix pnpm lockfile issues for Docker builds"
    echo ""
    echo "Options:"
    echo "  --backup-only    Only backup the current lockfile"
    echo "  --verify-only    Only verify lockfile consistency"
    echo "  --no-backup      Skip backing up current lockfile"
    echo "  -h, --help       Show this help"
    echo ""
    echo "Examples:"
    echo "  $0                    # Full fix: backup, clean, regenerate"
    echo "  $0 --verify-only      # Just check if lockfile is consistent"
    echo "  $0 --no-backup        # Fix without backing up current lockfile"
}

# Parse command line arguments
BACKUP_ONLY=0
VERIFY_ONLY=0
NO_BACKUP=0

while [[ $# -gt 0 ]]; do
    case $1 in
        --backup-only)
            BACKUP_ONLY=1
            shift
            ;;
        --verify-only)
            VERIFY_ONLY=1
            shift
            ;;
        --no-backup)
            NO_BACKUP=1
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "Starting pnpm lockfile fix process..."
    
    # Check prerequisites
    check_prerequisites
    
    # Verify only mode
    if [[ "$VERIFY_ONLY" == "1" ]]; then
        verify_lockfile
        exit $?
    fi
    
    # Backup only mode
    if [[ "$BACKUP_ONLY" == "1" ]]; then
        backup_lockfile
        exit 0
    fi
    
    # Full fix process
    if [[ "$NO_BACKUP" != "1" ]]; then
        backup_lockfile
    fi
    
    regenerate_lockfile
    
    if verify_lockfile; then
        print_success "Lockfile fix completed successfully!"
        print_status "You can now run Docker build commands"
    else
        print_error "Lockfile fix completed but verification failed"
        print_warning "You may need to manually resolve dependency conflicts"
        exit 1
    fi
}

# Run main function
main "$@"
