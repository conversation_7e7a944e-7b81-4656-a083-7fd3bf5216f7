#!/bin/bash

# Docker Build Optimization Script
# This script provides optimized Docker builds with better error handling and performance

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${IMAGE_NAME:-pluggou-web}"
TAG="${TAG:-latest}"
DOCKERFILE_PATH="${DOCKERFILE_PATH:-apps/web/Dockerfile}"
BUILD_CONTEXT="${BUILD_CONTEXT:-.}"
PLATFORM="${PLATFORM:-linux/amd64}"

# Build options
USE_BUILDKIT="${USE_BUILDKIT:-1}"
ENABLE_CACHE="${ENABLE_CACHE:-1}"
PARALLEL_BUILDS="${PARALLEL_BUILDS:-1}"
MAX_MEMORY="${MAX_MEMORY:-4g}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi

    # Check Docker version for BuildKit support
    DOCKER_VERSION=$(docker version --format '{{.Server.Version}}' 2>/dev/null || echo "0.0.0")
    if [[ $(echo "$DOCKER_VERSION 18.09.0" | tr " " "\n" | sort -V | head -n1) != "18.09.0" ]]; then
        print_warning "Docker version $DOCKER_VERSION may not support BuildKit optimizations"
        USE_BUILDKIT=0
    fi

    print_success "Prerequisites check passed"
}

# Function to setup BuildKit
setup_buildkit() {
    if [[ "$USE_BUILDKIT" == "1" ]]; then
        print_status "Setting up Docker BuildKit..."
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
        print_success "BuildKit enabled"
    fi
}

# Function to clean up old images and containers
cleanup_docker() {
    print_status "Cleaning up old Docker resources..."

    # Remove dangling images
    docker image prune -f &> /dev/null || true

    # Remove unused build cache (keep last 24h)
    if [[ "$USE_BUILDKIT" == "1" ]]; then
        docker buildx prune --filter until=24h -f &> /dev/null || true
    fi

    print_success "Docker cleanup completed"
}

# Function to build the Docker image
build_image() {
    print_status "Starting Docker build..."
    print_status "Image: $IMAGE_NAME:$TAG"
    print_status "Dockerfile: $DOCKERFILE_PATH"
    print_status "Context: $BUILD_CONTEXT"
    print_status "Platform: $PLATFORM"

    # Build arguments
    BUILD_ARGS=(
        "--file" "$DOCKERFILE_PATH"
        "--tag" "$IMAGE_NAME:$TAG"
        "--platform" "$PLATFORM"
    )

    # Add BuildKit specific optimizations
    if [[ "$USE_BUILDKIT" == "1" ]]; then
        BUILD_ARGS+=(
            "--build-arg" "BUILDKIT_INLINE_CACHE=1"
        )

        if [[ "$ENABLE_CACHE" == "1" ]]; then
            BUILD_ARGS+=(
                "--cache-from" "$IMAGE_NAME:latest"
                "--cache-from" "$IMAGE_NAME:cache"
            )
        fi
    fi

    # Resource limits
    BUILD_ARGS+=(
        "--memory" "$MAX_MEMORY"
        "--cpu-quota" "200000"  # 2 CPUs
        "--cpu-period" "100000"
    )

    # Progress output
    if [[ "$USE_BUILDKIT" == "1" ]]; then
        BUILD_ARGS+=("--progress" "plain")
    fi

    # Add build context
    BUILD_ARGS+=("$BUILD_CONTEXT")

    # Execute build with timeout
    print_status "Executing: docker build ${BUILD_ARGS[*]}"

    if timeout 1800 docker build "${BUILD_ARGS[@]}"; then
        print_success "Docker build completed successfully"
        return 0
    else
        local exit_code=$?
        print_error "Docker build failed or timed out (30 minutes)"

        # Check for common pnpm lockfile issues
        if docker logs $(docker ps -lq) 2>/dev/null | grep -q "lockfile.*mismatch\|frozen.*lockfile"; then
            print_warning "Detected pnpm lockfile mismatch. This can happen in Docker environments."
            print_warning "The Dockerfile includes fallback logic to handle this automatically."
            print_warning "If the issue persists, try: pnpm install && pnpm build locally first"
        fi

        return $exit_code
    fi
}

# Function to verify the built image
verify_image() {
    print_status "Verifying built image..."

    # Check if image exists
    if ! docker image inspect "$IMAGE_NAME:$TAG" &> /dev/null; then
        print_error "Built image not found"
        return 1
    fi

    # Get image size
    IMAGE_SIZE=$(docker image inspect "$IMAGE_NAME:$TAG" --format='{{.Size}}' | numfmt --to=iec)
    print_status "Image size: $IMAGE_SIZE"

    # Quick container test
    print_status "Testing container startup..."
    if timeout 30 docker run --rm -d --name "test-$IMAGE_NAME" -p 3001:3000 "$IMAGE_NAME:$TAG" &> /dev/null; then
        sleep 5
        if docker ps | grep -q "test-$IMAGE_NAME"; then
            print_success "Container started successfully"
            docker stop "test-$IMAGE_NAME" &> /dev/null || true
        else
            print_error "Container failed to start properly"
            return 1
        fi
    else
        print_error "Container startup test failed"
        return 1
    fi

    print_success "Image verification completed"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME          Image name (default: pluggou-web)"
    echo "  -t, --tag TAG            Image tag (default: latest)"
    echo "  -f, --file PATH          Dockerfile path (default: apps/web/Dockerfile)"
    echo "  -c, --context PATH       Build context (default: .)"
    echo "  -p, --platform PLATFORM  Target platform (default: linux/amd64)"
    echo "  --no-buildkit           Disable BuildKit"
    echo "  --no-cache              Disable build cache"
    echo "  --no-cleanup            Skip cleanup"
    echo "  --no-verify             Skip image verification"
    echo "  --memory LIMIT          Memory limit (default: 4g)"
    echo "  -h, --help              Show this help"
    echo ""
    echo "Environment variables:"
    echo "  IMAGE_NAME, TAG, DOCKERFILE_PATH, BUILD_CONTEXT, PLATFORM"
    echo "  USE_BUILDKIT, ENABLE_CACHE, MAX_MEMORY"
}

# Parse command line arguments
CLEANUP=1
VERIFY=1

while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE_PATH="$2"
            shift 2
            ;;
        -c|--context)
            BUILD_CONTEXT="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        --no-buildkit)
            USE_BUILDKIT=0
            shift
            ;;
        --no-cache)
            ENABLE_CACHE=0
            shift
            ;;
        --no-cleanup)
            CLEANUP=0
            shift
            ;;
        --no-verify)
            VERIFY=0
            shift
            ;;
        --memory)
            MAX_MEMORY="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "Starting optimized Docker build process..."

    # Check prerequisites
    check_prerequisites

    # Setup BuildKit
    setup_buildkit

    # Cleanup if requested
    if [[ "$CLEANUP" == "1" ]]; then
        cleanup_docker
    fi

    # Build the image
    if ! build_image; then
        print_error "Build failed"
        exit 1
    fi

    # Verify the image if requested
    if [[ "$VERIFY" == "1" ]]; then
        if ! verify_image; then
            print_error "Image verification failed"
            exit 1
        fi
    fi

    print_success "Docker build process completed successfully!"
    print_status "Image: $IMAGE_NAME:$TAG"
    print_status "To run: docker run -p 3000:3000 $IMAGE_NAME:$TAG"
}

# Run main function
main "$@"
