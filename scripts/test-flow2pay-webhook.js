#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test Flow2Pay webhook endpoint
 * This script sends test webhook events to verify the integration is working
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const fetch = require('node-fetch');

const WEBHOOK_URL = process.env.NEXT_PUBLIC_SITE_URL 
  ? `${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/flow2pay/baas`
  : 'http://localhost:3000/api/webhooks/flow2pay/baas';

const EVENT_TOKEN = process.env.FLOW2PAY_EVENT_TOKEN || 'test_token';

async function testWebhook() {
  console.log('🧪 Testing Flow2Pay webhook endpoint...');
  console.log(`📡 Webhook URL: ${WEBHOOK_URL}`);

  // Test 1: PixIn event (received payment)
  console.log('\n1️⃣ Testing PixIn event (received payment)...');
  const pixInPayload = {
    evento: 'PixIn',
    token: EVENT_TOKEN,
    txid: 'test_txid_' + Date.now(),
    endToEndId: 'E' + Math.floor(Math.random() * 1000000000000),
    valor: 1050, // R$ 10.50 in centavos
    horario: new Date().toISOString(),
    status: 'Sucesso',
    chavePix: '<EMAIL>',
    pagador: {
      nome: 'João Silva',
      chave: '<EMAIL>',
      tipoConta: 'CACC',
      cpf: '12345678901',
      codigoBanco: '260'
    }
  };

  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pixInPayload)
    });

    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.ok) {
      console.log('   ✅ PixIn webhook test passed');
    } else {
      console.log('   ❌ PixIn webhook test failed');
    }
  } catch (error) {
    console.log(`   ❌ PixIn webhook test error: ${error.message}`);
  }

  // Test 2: PixOut event (sent payment)
  console.log('\n2️⃣ Testing PixOut event (sent payment)...');
  const pixOutPayload = {
    evento: 'PixOut',
    token: EVENT_TOKEN,
    idEnvio: 'test_id_envio_' + Date.now(),
    endToEndId: 'E' + Math.floor(Math.random() * 1000000000000),
    valor: 500, // R$ 5.00 in centavos
    horario: new Date().toISOString(),
    status: 'Sucesso',
    chavePix: '<EMAIL>',
    recebedor: {
      nome: 'Maria Santos',
      codigoBanco: '341',
      cpf_cnpj: '98765432100'
    }
  };

  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pixOutPayload)
    });

    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.ok) {
      console.log('   ✅ PixOut webhook test passed');
    } else {
      console.log('   ❌ PixOut webhook test failed');
    }
  } catch (error) {
    console.log(`   ❌ PixOut webhook test error: ${error.message}`);
  }

  // Test 3: PixInReversal event (refund of received payment)
  console.log('\n3️⃣ Testing PixInReversal event (refund)...');
  const pixInReversalPayload = {
    evento: 'PixInReversal',
    token: EVENT_TOKEN,
    idEnvio: 'test_reversal_' + Date.now(),
    endToEndId: 'E' + Math.floor(Math.random() * 1000000000000),
    valor: 1050, // R$ 10.50 in centavos
    horario: new Date().toISOString(),
    status: 'Sucesso'
  };

  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pixInReversalPayload)
    });

    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.ok) {
      console.log('   ✅ PixInReversal webhook test passed');
    } else {
      console.log('   ❌ PixInReversal webhook test failed');
    }
  } catch (error) {
    console.log(`   ❌ PixInReversal webhook test error: ${error.message}`);
  }

  // Test 4: Invalid event type
  console.log('\n4️⃣ Testing invalid event type...');
  const invalidPayload = {
    evento: 'InvalidEvent',
    token: EVENT_TOKEN,
    valor: 1000,
    horario: new Date().toISOString()
  };

  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidPayload)
    });

    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.status === 400) {
      console.log('   ✅ Invalid event type properly rejected');
    } else {
      console.log('   ❌ Invalid event type should be rejected with 400 status');
    }
  } catch (error) {
    console.log(`   ❌ Invalid event test error: ${error.message}`);
  }

  // Test 5: Invalid token
  console.log('\n5️⃣ Testing invalid token...');
  const invalidTokenPayload = {
    evento: 'PixIn',
    token: 'invalid_token',
    txid: 'test_txid_invalid',
    valor: 1000,
    horario: new Date().toISOString(),
    status: 'Sucesso'
  };

  try {
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidTokenPayload)
    });

    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.status === 401) {
      console.log('   ✅ Invalid token properly rejected');
    } else {
      console.log('   ⚠️  Invalid token test - may pass if no token validation configured');
    }
  } catch (error) {
    console.log(`   ❌ Invalid token test error: ${error.message}`);
  }

  console.log('\n🎯 Webhook testing completed!');
  console.log('\n📋 Summary:');
  console.log('   - PixIn events: Payment received notifications');
  console.log('   - PixOut events: Payment sent notifications');
  console.log('   - PixInReversal events: Refund notifications');
  console.log('   - Error handling: Invalid events and tokens');
  console.log('\n💡 Next steps:');
  console.log('   1. Check application logs for webhook processing details');
  console.log('   2. Verify database transactions are created/updated');
  console.log('   3. Test with real Flow2Pay webhook events');
}

// Run the test
testWebhook().catch(console.error);
